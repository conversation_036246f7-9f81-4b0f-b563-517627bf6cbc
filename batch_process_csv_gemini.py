
import pandas as pd
import os
import glob
from typing import Tuple, List, Callable, Any
import time
import logging
import google.generativeai as genai
import re
import requests
import json
import argparse
from functools import wraps
import random

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置Gemini API
GEMINI_API_KEY = "AIzaSyAd81Lk9pZwH9LLh36sq5OmlMnJFss7SIw"

# API重试配置
API_RETRY_CONFIG = {
    'max_retries': 5,           # 最大重试次数
    'base_delay': 2,            # 基础延迟时间（秒）
    'max_delay': 60,            # 最大延迟时间（秒）
    'backoff_factor': 2,        # 退避因子
    'jitter': True,             # 是否添加随机抖动
    'retry_on_status': [429, 500, 502, 503, 504],  # 需要重试的HTTP状态码
}

def api_retry_decorator(max_retries: int = None, base_delay: float = None):
    """
    API请求重试装饰器
    
    Args:
        max_retries: 最大重试次数，如果为None则使用全局配置
        base_delay: 基础延迟时间，如果为None则使用全局配置
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # 使用传入的参数或全局配置
            retries = max_retries if max_retries is not None else API_RETRY_CONFIG['max_retries']
            delay = base_delay if base_delay is not None else API_RETRY_CONFIG['base_delay']
            
            last_exception = None
            
            for attempt in range(retries + 1):  # +1 因为第一次不算重试
                try:
                    if attempt > 0:
                        # 计算延迟时间（指数退避 + 随机抖动）
                        current_delay = min(
                            delay * (API_RETRY_CONFIG['backoff_factor'] ** (attempt - 1)),
                            API_RETRY_CONFIG['max_delay']
                        )
                        
                        # 添加随机抖动以避免雷群效应
                        if API_RETRY_CONFIG['jitter']:
                            current_delay += random.uniform(0, current_delay * 0.1)
                        
                        logger.info(f"API请求重试 {attempt}/{retries}，等待 {current_delay:.2f} 秒...")
                        time.sleep(current_delay)
                    
                    # 执行API调用
                    result = func(*args, **kwargs)
                    
                    # 检查结果是否有效
                    if isinstance(result, tuple) and len(result) == 2:
                        entity_annotations, relation_annotations = result
                        # 如果两个都为空，可能是API返回了无效结果
                        if not entity_annotations and not relation_annotations:
                            if attempt < retries:
                                logger.warning(f"API返回空结果，准备重试...")
                                continue
                        return result
                    else:
                        return result
                        
                except requests.exceptions.Timeout as e:
                    last_exception = e
                    logger.warning(f"API请求超时 (尝试 {attempt + 1}/{retries + 1}): {str(e)}")
                    
                except requests.exceptions.ConnectionError as e:
                    last_exception = e
                    logger.warning(f"API连接错误 (尝试 {attempt + 1}/{retries + 1}): {str(e)}")
                    
                except requests.exceptions.HTTPError as e:
                    last_exception = e
                    if hasattr(e, 'response') and e.response.status_code in API_RETRY_CONFIG['retry_on_status']:
                        logger.warning(f"API HTTP错误 {e.response.status_code} (尝试 {attempt + 1}/{retries + 1}): {str(e)}")
                    else:
                        logger.error(f"API HTTP错误，不重试: {str(e)}")
                        break
                        
                except Exception as e:
                    last_exception = e
                    # 对于某些特定错误，我们可能不想重试
                    error_str = str(e).lower()
                    if any(keyword in error_str for keyword in ['invalid api key', 'authentication', 'permission']):
                        logger.error(f"API认证错误，不重试: {str(e)}")
                        break
                    else:
                        logger.warning(f"API调用异常 (尝试 {attempt + 1}/{retries + 1}): {str(e)}")
            
            # 所有重试都失败了
            logger.error(f"API请求失败，已达到最大重试次数 {retries}")
            if last_exception:
                logger.error(f"最后一次错误: {str(last_exception)}")
            
            # 返回空结果而不是抛出异常，让调用者处理
            return "", ""
            
        return wrapper
    return decorator

def clear_annotations_from_csv(file_path: str) -> bool:
    """
    清理CSV文件中的已有标注
    """
    try:
        logger.info(f"正在清理文件 {file_path} 中的已有标注...")
        
        # 尝试多种编码读取文件
        df = None
        encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                break
            except UnicodeDecodeError:
                continue
        
        if df is None:
            logger.error(f"无法读取文件 {file_path}")
            return False
        
        # 清空实体标注和关系标注列
        if '实体标注' in df.columns:
            df['实体标注'] = ''
        if '关系标注' in df.columns:
            df['关系标注'] = ''
        
        # 保存文件
        df.to_csv(file_path, index=False, encoding='utf-8-sig')
        logger.info(f"文件 {file_path} 标注清理完成")
        
        return True
        
    except Exception as e:
        logger.error(f"清理文件 {file_path} 时出错: {str(e)}")
        return False

def clear_all_annotations(directory_path: str = ".") -> None:
    """
    清理目录中所有CSV文件的标注
    """
    csv_files = sorted(glob.glob(os.path.join(directory_path, "*.csv")))
    
    if not csv_files:
        logger.warning(f"在目录 {directory_path} 中没有找到CSV文件")
        return
    
    logger.info(f"开始清理 {len(csv_files)} 个CSV文件的标注...")
    
    success_count = 0
    failed_count = 0
    
    for i, file_path in enumerate(csv_files, 1):
        logger.info(f"进度: {i}/{len(csv_files)} - 正在清理 {os.path.basename(file_path)}")
        
        if clear_annotations_from_csv(file_path):
            success_count += 1
        else:
            failed_count += 1
    
    logger.info(f"标注清理完成！总计 {len(csv_files)} 个文件，成功 {success_count} 个，失败 {failed_count} 个")

def safe_str_conversion(value) -> str:
    """
    安全地将值转换为字符串，确保数据保存正确
    """
    if pd.isna(value) or value is None:
        return ""
    return str(value)

@api_retry_decorator()
def analyze_text_with_gemini_rest(text: str) -> Tuple[str, str]:
    """
    使用REST API直接调用Gemini，带重试机制
    """
    
    prompt = f"""你是一个专业的领域知识图谱标注助手，专注于"邻近铁路营业线施工管理"领域。你的任务是根据我提供的实体和关系标签体系，对给定的文本片段进行预标注建议。请严格遵循以下定义和输出格式。

一、 实体标签体系：
{{
"组织机构": ["组织机构 (ORG)"],
"人员": ["人员 (PERSON)"],
"角色/岗位": ["角色/岗位 (ROLE)"],
"铁路专业": [
"工务专业 (SPECIALTY_CIVIL)",
"电务专业 (SPECIALTY_SIGNAL)",
"供电专业 (SPECIALTY_POWER)",
"房建专业 (SPECIALTY_BUILDING)",
"通信专业 (SPECIALTY_COMM)"
],
"活动/流程": [
"施工作业 (ACT_CONSTRUCTION)",
"维修作业 (ACT_MAINTENANCE)",
"计划管理 (ACT_PLAN_MGMT)",
"方案管理 (ACT_SOLUTION_MGMT)",
"安全管理 (ACT_SAFETY_MGMT)",
"质量管理 (ACT_QUALITY_MGMT)",
"检查/监控 (ACT_INSPECT_MONITOR)",
"应急处置 (ACT_EMERGENCY_RESPONSE)",
"验收交接 (ACT_ACCEPT_HANDOVER)",
"培训 (ACT_TRAINING)",
"登销记 (ACT_REG_CANCEL)",
"施工类型 (TYPE_CONSTRUCTION)",
"工艺工法 (METHOD_PROCESS)"
],
"文档/规则": [
"规章制度 (DOC_REGULATION)",
"技术标准 (DOC_STANDARD_TECH)",
"施工方案 (DOC_PLAN_CONST)",
"维修方案 (DOC_PLAN_MAINT)",
"施工计划 (DOC_SCHEDULE_CONST)",
"维修计划 (DOC_SCHEDULE_MAINT)",
"安全协议 (DOC_AGREEMENT_SAFETY)",
"许可/批复 (DOC_PERMIT_APPROVAL)",
"调度命令 (DOC_DISPATCH_ORDER)",
"报告/记录 (DOC_REPORT_RECORD)",
"安全红线 (RULE_SAFETY_REDLINE)",
"证书/资质 (DOC_CERTIFICATE)"
],
"位置/设施": [
"铁路营业线 (LOC_LINE_OPERATIONAL)",
"站场 (LOC_STATION_YARD)",
"区间 (LOC_SECTION_INTERCITY)",
"施工现场 (LOC_CONSTRUCTION_SITE)",
"安全界限 (FAC_SAFETY_BOUNDARY)",
"物理隔离 (FAC_ISOLATION_PHYSICAL)",
"机械设备 (EQUIP_MACHINERY)",
"材料路料 (MATERIAL)"
],
"风险/事件": [
"风险 (RISK)",
"隐患 (HAZARD)",
"事故 (ACCIDENT)",
"问题 (ISSUE)",
"处罚 (PENALTY)",
"慢行/限速 (CONDITION_SPEED_LIMIT)",
"天窗 (WINDOW_POSSESSION)",
"考核和奖惩 (ASSESSMENT_REWARD)"
],
"参数信息": ["参数信息 (PARAM_INFO)"]
}}

二、 关系标签体系 (主语实体类型 -> 关系标签 (中文) -> 宾语实体类型)：
{{
"从属/层级关系": [
"人员 -> 所属 -> 组织机构",
"具体安全措施(作为文档一部分或被识别的片段) -> 是组成部分 -> 施工方案",
"规章制度 -> 包含 -> 安全红线"
],
"职责/负责关系": [
"组织机构/角色/岗位 -> 负责 -> 活动/流程/风险/事件",
"组织机构 -> 协作 -> 组织机构",
"组织机构/角色/岗位 -> 管理 -> 活动/流程/位置/设施",
"角色/岗位 -> 盯控 -> 活动/流程",
"组织机构/角色/岗位 -> 监护 -> 活动/流程",
"人员 -> 担任 -> 角色/岗位"
],
"活动/过程关系": [
"组织机构/人员 -> 执行 -> 文档/规则 (如施工方案)",
"组织机构/人员 -> 参与 -> 活动/流程",
"组织机构/角色/岗位 -> 组织 -> 活动/流程 (如培训、会议)",
"活动/流程 -> 需要 -> 文档/规则/位置/设施/天窗",
"活动/流程 -> 使用 -> 位置/设施 (如机械设备)/天窗",
"证书/资质 -> 是...的资质 -> 角色/岗位 或 活动/流程",
"组织机构 -> 进行考核 -> 组织机构/人员",
"组织机构/人员 -> 提供 -> 文档/规则/材料路料",
"角色/岗位 -> 联络 -> 角色/岗位"
],
"文件/方案关系": [
"组织机构/人员 -> 编制/制定 -> 文档/规则 (如施工方案、计划)",
"组织机构/角色/岗位 -> 审核 -> 文档/规则",
"组织机构/角色/岗位 -> 审批 -> 文档/规则",
"组织机构 -> 下达 -> 文档/规则 (如调度命令、计划)",
"活动/流程/文档 -> 依据 -> 文档/规则 (如规章制度、技术标准)",
"组织机构 -> 签订 -> 安全协议 (与另一 组织机构)",
"组织机构/人员 -> 上交/上报 -> 组织机构",
"规章制度/技术标准 -> 规定 -> (具体规定内容文本片段 或 其他相关实体)",
"规章制度 -> 替代/废止 -> 规章制度",
"报告/记录 -> 记录 -> 活动/流程/风险/事件",
"人员/组织机构 -> 取得 -> 证书/资质 或 许可/批复",
"组织机构/人员 -> 处理 -> 问题/风险/事件 (通过文件方式)",
"文档/规则 -> 关于 -> (相关主题实体)",
"技术标准/规章制度 -> 适用于 -> 铁路专业/施工类型/机械设备"
],
"安全/风险关系": [
"风险/隐患/问题/违规行为(作为问题的一种) -> 导致 -> 事故/风险/处罚",
"安全管理/安全措施(作为文档一部分) -> 控制 -> 风险/隐患",
"检查/监控 -> 发现 -> 风险/隐患/问题",
"组织机构/人员 -> 整改 -> 隐患/问题",
"组织机构/人员 -> 受到 -> 处罚/考核和奖惩",
"活动/流程/人员 -> 违反 -> 安全红线/规章制度",
"组织机构/人员 -> 接受 -> (监督，宾语是 组织机构)",
"施工作业/维修作业 -> 受约束于 -> 慢行/限速/安全界限/天窗"
],
"地点/空间关系": [
"位置/设施/活动/流程 -> 位于 -> 位置/设施",
"位置/设施/活动/流程 -> 邻近 -> 位置/设施 (特别是铁路营业线)",
"风险/隐患 -> 存在于 -> 位置/设施",
"位置/设施 -> 是...场所 -> 活动/流程"
],
"量化/约束关系": [
"天窗/慢行/限速/安全界限/施工方案/施工计划/其他相关实体 -> 数值要求 -> 参数信息",
"施工方案/施工计划/报告/记录/其他相关实体 -> 日期要求 -> 参数信息"
]
}}

请分析以下文本并给出标注建议：
【待标注文本片段】：{text}

请按以下格式输出：
实体标注建议：
【实体文本片段】: 实体标签

关系标注建议：
【主语实体文本片段】(主语实体标签) -关系标签-> 【宾语实体文本片段】(宾语实体标签)
"""
    
    # 使用REST API调用
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={GEMINI_API_KEY}"
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    data = {
        "contents": [
            {
                "parts": [
                    {
                        "text": prompt
                    }
                ]
            }
        ]
    }
    
    # 发送请求，设置30秒超时
    response = requests.post(url, headers=headers, json=data, timeout=30)
    
    # 检查HTTP状态码
    if response.status_code in API_RETRY_CONFIG['retry_on_status']:
        raise requests.exceptions.HTTPError(f"HTTP {response.status_code}", response=response)
    
    response.raise_for_status()  # 抛出HTTP错误
    
    result = response.json()
    if 'candidates' in result and len(result['candidates']) > 0:
        text_content = result['candidates'][0]['content']['parts'][0]['text']
        return parse_gemini_response(text_content)
    else:
        logger.warning("API响应中没有找到内容")
        return "", ""

@api_retry_decorator()
def analyze_text_with_gemini_sdk(text: str) -> Tuple[str, str]:
    """
    使用SDK调用Gemini（备用方法），带重试机制
    """
    genai.configure(api_key=GEMINI_API_KEY)
    
    # 使用更新的模型名称
    model = genai.GenerativeModel('gemini-2.0-flash')
    
    prompt = f"""请对以下文本进行实体和关系标注：
{text}

请按以下格式输出：
实体标注建议：
【实体】: 标签

关系标注建议：
【主语】(标签) -关系-> 【宾语】(标签)
"""
    
    # 设置生成配置
    generation_config = genai.types.GenerationConfig(
        temperature=0
    )
    
    response = model.generate_content(prompt, generation_config=generation_config)
    
    if response.text:
        return parse_gemini_response(response.text)
    else:
        logger.warning("SDK返回空响应")
        return "", ""

def parse_gemini_response(response_text: str) -> Tuple[str, str]:
    """
    解析Gemini API的响应，提取实体标注和关系标注
    """
    try:
        lines = response_text.strip().split('\n')
        
        entity_annotations = []
        relation_annotations = []
        
        current_section = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 识别章节
            if "实体标注建议" in line or "实体标注" in line:
                current_section = "entity"
                continue
            elif "关系标注建议" in line or "关系标注" in line:
                current_section = "relation"
                continue
            
            # 提取实体标注
            if current_section == "entity":
                # 匹配多种格式
                entity_match = re.search(r'【([^】]+)】\s*[:：]\s*(.+)', line)
                if entity_match:
                    entity_text = entity_match.group(1)
                    entity_label = entity_match.group(2).strip()
                    entity_annotations.append(f"【{entity_text}】: {entity_label}")
            
            # 提取关系标注
            elif current_section == "relation":
                # 匹配关系格式
                relation_match = re.search(r'【([^】]+)】\(([^)]+)\)\s*-([^-]+)->\s*【([^】]+)】\(([^)]+)\)', line)
                if relation_match:
                    subject = relation_match.group(1)
                    subject_label = relation_match.group(2)
                    relation = relation_match.group(3)
                    object_text = relation_match.group(4)
                    object_label = relation_match.group(5)
                    relation_annotations.append(f"【{subject}】({subject_label}) -{relation}-> 【{object_text}】({object_label})")
        
        # 格式化输出
        entity_output = "; ".join(entity_annotations)
        relation_output = "; ".join(relation_annotations)
        
        return entity_output, relation_output
        
    except Exception as e:
        logger.error(f"解析Gemini响应时出错: {str(e)}")
        return "", ""

def test_gemini_api():
    """
    测试Gemini API连接
    """
    test_text = "本办法适用于国家铁路、地方铁路营业线施工安全管理。"
    
    logger.info("测试Gemini API连接...")
    
    # 首先尝试REST API
    logger.info("尝试使用REST API...")
    entity_annotations, relation_annotations = analyze_text_with_gemini_rest(test_text)
    
    if entity_annotations or relation_annotations:
        logger.info("REST API测试成功！")
        logger.info(f"测试结果 - 实体标注: {entity_annotations}")
        logger.info(f"测试结果 - 关系标注: {relation_annotations}")
        return True
    
    # 如果REST API失败，尝试SDK
    logger.info("REST API失败，尝试使用SDK...")
    entity_annotations, relation_annotations = analyze_text_with_gemini_sdk(test_text)
    
    if entity_annotations or relation_annotations:
        logger.info("SDK测试成功！")
        logger.info(f"测试结果 - 实体标注: {entity_annotations}")
        logger.info(f"测试结果 - 关系标注: {relation_annotations}")
        return True
    
    logger.error("所有API方法都失败了！")
    return False

def process_single_csv(file_path: str, force_reprocess: bool = False, max_retries: int = 3) -> bool:
    """
    处理单个CSV文件
    
    Args:
        file_path: CSV文件路径
        force_reprocess: 是否强制重新处理已有标注的行
        max_retries: 每行最大重试次数
    """
    try:
        logger.info(f"正在处理文件: {file_path}")
        
        # 读取CSV文件，尝试多种编码
        df = None
        encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                logger.info(f"成功使用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError:
                continue
        
        if df is None:
            logger.error(f"无法读取文件 {file_path}，尝试了所有编码方式")
            return False
        
        # 检查必要的列是否存在
        if 'text' not in df.columns:
            logger.warning(f"文件 {file_path} 中没有找到 'text' 列")
            return False
        
        # 确保实体标注和关系标注列存在
        if '实体标注' not in df.columns:
            df['实体标注'] = ''
        if '关系标注' not in df.columns:
            df['关系标注'] = ''
        
        # 统计处理信息
        total_rows = len(df)
        processed_rows = 0
        skipped_rows = 0
        
        # 处理每一行的text内容
        for index, row in df.iterrows():
            text_content = row['text']
            
            # 跳过空文本
            if pd.isna(text_content) or str(text_content).strip() == '':
                continue
            
            # 检查是否需要跳过已有标注的行
            if not force_reprocess:
                if (pd.notna(row['实体标注']) and str(row['实体标注']).strip() != ''):
                    logger.info(f"文件 {file_path} 第 {index+1} 行已有标注，跳过")
                    skipped_rows += 1
                    continue
            
            # 调用Gemini API分析，带重试机制
            retry_count = 0
            success = False
            
            while retry_count < max_retries and not success:
                try:
                    logger.info(f"正在分析文件 {file_path} 第 {index+1} 行... (尝试 {retry_count + 1}/{max_retries})")
                    
                    # 优先使用REST API
                    entity_annotations, relation_annotations = analyze_text_with_gemini_rest(str(text_content))
                    
                    # 如果REST API失败，尝试SDK
                    if not entity_annotations and not relation_annotations:
                        entity_annotations, relation_annotations = analyze_text_with_gemini_sdk(str(text_content))
                    
                    # 确保数据类型正确并安全转换
                    entity_annotations = safe_str_conversion(entity_annotations)
                    relation_annotations = safe_str_conversion(relation_annotations)
                    
                    # 更新DataFrame
                    df.at[index, '实体标注'] = entity_annotations
                    df.at[index, '关系标注'] = relation_annotations
                    
                    logger.info(f"完成文件 {file_path} 第 {index+1} 行的标注")
                    processed_rows += 1
                    success = True
                    
                    # 添加延迟以避免API限制
                    time.sleep(2)
                    
                except Exception as e:
                    retry_count += 1
                    logger.error(f"处理文件 {file_path} 第 {index+1} 行时出错 (尝试 {retry_count}/{max_retries}): {str(e)}")
                    
                    if retry_count < max_retries:
                        logger.info(f"等待 5 秒后重试...")
                        time.sleep(5)
                    else:
                        logger.error(f"文件 {file_path} 第 {index+1} 行处理失败，已达到最大重试次数")
        
        # 保存更新后的文件，确保数据类型正确
        try:
            # 确保所有标注列都是字符串类型
            df['实体标注'] = df['实体标注'].apply(safe_str_conversion)
            df['关系标注'] = df['关系标注'].apply(safe_str_conversion)
            
            # 保存文件
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
            logger.info(f"文件 {file_path} 处理完成并已保存")
            logger.info(f"处理统计: 总行数 {total_rows}, 处理行数 {processed_rows}, 跳过行数 {skipped_rows}")
            
        except Exception as e:
            logger.error(f"保存文件 {file_path} 时出错: {str(e)}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"处理文件 {file_path} 时出错: {str(e)}")
        return False

def process_all_csv_files(directory_path: str = ".", force_reprocess: bool = False, max_files: int = None) -> None:
    """
    批量处理目录中的所有CSV文件
    
    Args:
        directory_path: 目录路径
        force_reprocess: 是否强制重新处理已有标注的行
        max_files: 最大处理文件数量，用于避免无限循环
    """
    # 获取所有CSV文件并排序
    csv_files = sorted(glob.glob(os.path.join(directory_path, "*.csv")))
    
    if not csv_files:
        logger.warning(f"在目录 {directory_path} 中没有找到CSV文件")
        return
    
    # 限制处理文件数量以避免无限循环
    if max_files and len(csv_files) > max_files:
        csv_files = csv_files[:max_files]
        logger.info(f"限制处理文件数量为 {max_files} 个")
    
    logger.info(f"找到 {len(csv_files)} 个CSV文件")
    
    # 统计信息
    success_count = 0
    failed_count = 0
    
    # 处理每个文件
    for i, file_path in enumerate(csv_files, 1):
        logger.info(f"进度: {i}/{len(csv_files)} - 正在处理 {os.path.basename(file_path)}")
        
        if process_single_csv(file_path, force_reprocess):
            success_count += 1
        else:
            failed_count += 1
        
        # 每处理10个文件显示一次进度
        if i % 10 == 0:
            logger.info(f"已处理 {i} 个文件，成功 {success_count} 个，失败 {failed_count} 个")
    
    # 最终统计
    logger.info(f"批量处理完成！总计 {len(csv_files)} 个文件，成功 {success_count} 个，失败 {failed_count} 个")

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='批量处理CSV文件进行实体和关系标注')
    parser.add_argument('--clear', action='store_true', help='清理所有CSV文件中的已有标注')
    parser.add_argument('--force', action='store_true', help='强制重新处理已有标注的行')
    parser.add_argument('--max-files', type=int, help='最大处理文件数量')
    parser.add_argument('--directory', type=str, default='.', help='处理的目录路径')
    
    args = parser.parse_args()
    
    if args.clear:
        logger.info("开始清理所有CSV文件中的标注...")
        clear_all_annotations(args.directory)
        return
    
    logger.info("开始批量处理CSV文件...")
    
    # 首先测试API连接
    if not test_gemini_api():
        logger.error("API测试失败，请检查API密钥和网络连接")
        return
    
    # 处理CSV文件
    process_all_csv_files(args.directory, args.force, args.max_files)
    
    logger.info("批量处理完成！")

if __name__ == "__main__":
    main()



